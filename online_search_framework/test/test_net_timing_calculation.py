#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试净耗时计算逻辑
验证时间统计是否按照净耗时方式正确计算
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from smoke_test import OnlineSearchSmokeTest


def test_net_timing_calculation():
    """测试净耗时计算逻辑"""
    print("🧮 测试净耗时计算逻辑")
    print("=" * 50)
    
    # 创建测试实例
    smoke_test = OnlineSearchSmokeTest()
    
    # 模拟响应数据（累计时间）
    mock_response_data = {
        "time_cost": {
            "t1-loc": 0.1,
            "t2-mem": 0.3,
            "t3-pre_intent": 1.5,
            "t3-intent": 2.0,
            "t4-src": 4.0,
            "t5-fetch": 9.0,
            "t6-mft": 9.5,
            "t7-sft": 10.0,
            "t8-acft": 3.0,
            "total_time": 10.0
        },
        "answer": "测试答案",
        "sources": [{"url": "test1"}, {"url": "test2"}]
    }
    
    print("📊 原始时间数据（累计时间）:")
    for key, value in mock_response_data["time_cost"].items():
        print(f"   {key}: {value}s")
    
    print("\n🔄 净耗时计算过程:")
    
    # 提取时间信息
    timing_info = smoke_test.extract_timing_info(mock_response_data)
    
    print("📈 净耗时结果:")
    for key, value in timing_info.items():
        print(f"   {key}: {value}s")
    
    print("\n✅ 验证净耗时计算:")
    
    # 验证计算是否正确
    expected_results = {
        "t1-loc": 0.1,  # 保持原值
        "t2-mem": 0.3 - 0.1,  # 0.2
        "t3-pre_intent": 1.5 - 0.3,  # 1.2
        "t3-intent": 2.0 - 1.5,  # 0.5
        "t4-src": 4.0 - 2.0,  # 2.0
        "t5-fetch": 9.0 - 4.0,  # 5.0
        "t6-mft": 9.5 - 9.0,  # 0.5
        "t7-sft": round(10.0 - 9.5, 6),  # 0.5
        "t8-acft": 3.0,  # 保持原值
        "total_time": 10.0  # 保持原值
    }
    
    all_correct = True
    for key, expected in expected_results.items():
        actual = timing_info.get(key, 0)
        if abs(actual - expected) < 0.000001:  # 浮点数比较
            print(f"   ✓ {key}: {actual}s (期望: {expected}s)")
        else:
            print(f"   ✗ {key}: {actual}s (期望: {expected}s)")
            all_correct = False
    
    print(f"\n🎯 计算结果: {'✅ 全部正确' if all_correct else '❌ 存在错误'}")
    
    # 测试格式化输出
    print("\n📝 格式化输出测试:")
    timing_summary = smoke_test.format_timing_summary(timing_info)
    print(f"   {timing_summary}")
    
    return all_correct


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("=" * 50)
    
    smoke_test = OnlineSearchSmokeTest()
    
    # 测试空数据
    print("1. 测试空数据:")
    empty_data = {}
    timing_info = smoke_test.extract_timing_info(empty_data)
    print(f"   结果: {timing_info}")
    
    # 测试缺失字段
    print("\n2. 测试缺失字段:")
    partial_data = {
        "time_cost": {
            "t1-loc": 0.1,
            "t3-intent": 2.0,
            "total_time": 5.0
        }
    }
    timing_info = smoke_test.extract_timing_info(partial_data)
    print(f"   结果: {timing_info}")
    
    # 测试无效数据类型
    print("\n3. 测试无效数据类型:")
    invalid_data = {
        "time_cost": {
            "t1-loc": "invalid",
            "t2-mem": None,
            "t3-intent": 2.0
        }
    }
    timing_info = smoke_test.extract_timing_info(invalid_data)
    print(f"   结果: {timing_info}")
    
    return True


def main():
    """主函数"""
    print("🧪 净耗时计算测试")
    print("=" * 80)
    
    # 运行测试
    test1_result = test_net_timing_calculation()
    test2_result = test_edge_cases()
    
    print("\n" + "=" * 80)
    print("📊 测试总结:")
    print(f"   净耗时计算测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   边界情况测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result
    
    if overall_success:
        print("🎉 所有测试都通过了！净耗时计算逻辑正确")
    else:
        print("⚠️ 部分测试失败，请检查计算逻辑")
    
    print("\n📖 净耗时计算说明:")
    print("   t1-loc: 保持原值")
    print("   t2-mem: t2_mem - t1_loc")
    print("   t3-pre_intent: t3_pre_intent - t2_mem")
    print("   t3-intent: t3_intent - t3_pre_intent")
    print("   t4-src: t4_src - t3_intent")
    print("   t5-fetch: t5_fetch - t4_src")
    print("   t6-mft: t6_mft - t5_fetch")
    print("   t7-sft: round(t7_sft - t6_mft, 6)")
    print("   t8-acft: 保持原值")
    print("   total_time: 保持原值")
    
    print("=" * 80)
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
