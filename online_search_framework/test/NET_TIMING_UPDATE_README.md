# 净耗时计算更新说明

## 更新概述

已将 `online_search_framework/test/smoke_test.py` 中的性能测试时间统计从累计耗时改为净耗时计算方式。

## 修改内容

### 1. 更新 `extract_timing_info` 方法

**修改前**: 直接使用响应中的累计时间
```python
timing_info[field] = float(time_cost[field])
```

**修改后**: 按净耗时计算各阶段耗时
```python
# 按净耗时计算方式计算各阶段耗时
timing_info["t1-loc"] = raw_times.get("t1-loc", 0)
timing_info["t2-mem"] = raw_times.get("t2-mem", 0) - raw_times.get("t1-loc", 0)
timing_info["t3-pre_intent"] = raw_times.get("t3-pre_intent", 0) - raw_times.get("t2-mem", 0)
timing_info["t3-intent"] = raw_times.get("t3-intent", 0) - raw_times.get("t3-pre_intent", 0)
timing_info["t4-src"] = raw_times.get("t4-src", 0) - raw_times.get("t3-intent", 0)
timing_info["t5-fetch"] = raw_times.get("t5-fetch", 0) - raw_times.get("t4-src", 0)
timing_info["t6-mft"] = raw_times.get("t6-mft", 0) - raw_times.get("t5-fetch", 0)
timing_info["t7-sft"] = round(raw_times.get("t7-sft", 0) - raw_times.get("t6-mft", 0), 6)
timing_info["t8-acft"] = raw_times.get("t8-acft", 0)
timing_info["total_time"] = raw_times.get("total_time", 0)
```

### 2. 更新字段名称说明

在 `format_timing_summary` 和性能统计报告中，为净耗时字段添加了 "(净耗时)" 标识：

```python
time_field_names = {
    "t1-loc": "高德经纬度转换成城市",
    "t2-mem": "获取用户画像(净耗时)",
    "t3-pre_intent": "前置落域(净耗时)",
    "t3-intent": "意图识别(净耗时)",
    "t4-src": "搜索引擎返回结果(净耗时)",
    "t5-fetch": "爬虫结束(净耗时)",
    "t6-mft": "大模型总结首字延迟(净耗时)",
    "t7-sft": "安全检测首字延迟(净耗时)",
    "t8-acft": "原子能力接口首字延迟",
    "total_time": "总耗时"
}
```

### 3. 增强调试信息

在性能测试中添加了更详细的调试信息：
- 显示净耗时详情
- 显示原始时间数据（用于调试）

## 净耗时计算规则

| 字段 | 计算方式 | 说明 |
|------|----------|------|
| t1-loc | 保持原值 | 高德经纬度转换成城市 |
| t2-mem | t2_mem - t1_loc | 获取用户画像的净耗时 |
| t3-pre_intent | t3_pre_intent - t2_mem | 前置落域的净耗时 |
| t3-intent | t3_intent - t3_pre_intent | 意图识别的净耗时 |
| t4-src | t4_src - t3_intent | 搜索引擎返回结果的净耗时 |
| t5-fetch | t5_fetch - t4_src | 爬虫结束的净耗时 |
| t6-mft | t6_mft - t5_fetch | 大模型总结首字延迟的净耗时 |
| t7-sft | round(t7_sft - t6_mft, 6) | 安全检测首字延迟的净耗时 |
| t8-acft | 保持原值 | 原子能力接口首字延迟 |
| total_time | 保持原值 | 总耗时 |

## 测试验证

创建了 `test_net_timing_calculation.py` 来验证净耗时计算逻辑：

### 测试用例
```python
# 原始时间数据（累计时间）
mock_data = {
    "t1-loc": 0.1,
    "t2-mem": 0.3,
    "t3-pre_intent": 1.5,
    "t3-intent": 2.0,
    "t4-src": 4.0,
    "t5-fetch": 9.0,
    "t6-mft": 9.5,
    "t7-sft": 10.0,
    "t8-acft": 3.0,
    "total_time": 10.0
}

# 期望的净耗时结果
expected_results = {
    "t1-loc": 0.1,
    "t2-mem": 0.2,      # 0.3 - 0.1
    "t3-pre_intent": 1.2, # 1.5 - 0.3
    "t3-intent": 0.5,   # 2.0 - 1.5
    "t4-src": 2.0,      # 4.0 - 2.0
    "t5-fetch": 5.0,    # 9.0 - 4.0
    "t6-mft": 0.5,      # 9.5 - 9.0
    "t7-sft": 0.5,      # 10.0 - 9.5
    "t8-acft": 3.0,
    "total_time": 10.0
}
```

### 测试结果
✅ 所有测试都通过了！净耗时计算逻辑正确

## 使用方法

运行更新后的冒烟测试：
```bash
cd online_search_framework/test
python3 smoke_test.py
```

验证净耗时计算：
```bash
python3 test_net_timing_calculation.py
```

## 输出示例

更新后的性能测试输出将显示净耗时信息：
```
第1轮: 5.23s (来源数量: 3)
净耗时详情: [高德经纬度转换成城市:0.10s 获取用户画像(净耗时):0.20s 前置落域(净耗时):1.20s ...]
原始时间数据: {'t1-loc': 0.1, 't2-mem': 0.3, 't3-pre_intent': 1.5, ...}
```

## 兼容性

- ✅ 保持了原有的接口和数据结构
- ✅ 向后兼容，不影响其他功能
- ✅ 增加了错误处理，处理缺失或无效数据
- ✅ 保留了原始时间数据的调试输出

## 注意事项

1. **浮点数精度**: 由于浮点数运算，可能出现微小的精度差异（如 0.19999999999999998）
2. **缺失数据处理**: 当某些时间字段缺失时，会使用 0 作为默认值进行计算
3. **负值处理**: 如果时间数据不合理（后一个时间点小于前一个），可能出现负值
4. **调试信息**: 在测试过程中会显示详细的时间计算信息，便于问题排查

## 相关文件

- `smoke_test.py` - 主要的冒烟测试文件（已更新）
- `test_net_timing_calculation.py` - 净耗时计算验证脚本（新增）
- `NET_TIMING_UPDATE_README.md` - 本说明文档（新增）
