import asyncio
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional
from typing import Union, Dict, List
import time
import openai
import requests
import json
import os
import aiohttp, asyncio
from loguru import logger
from openai import APIError, APIStatusError
from starlette.concurrency import run_in_threadpool
from configuration import config
import re
import ast
import src.utils.news_error_code as news_error_code


from nova.security_policy import (
    TextModerationRequest,
    stream_output_moderate_text,
    moderate_text_generic,
)
# from service.logger_manager import fire_logger
from src.prompts.follow_up_prompts import follow_up_prompts
from src.prompts.guide_prompts import guide_user_prompt_zh, guide_system_prompt_zh
from src.prompts.router_prompts import router_user_zh, router_system_zh
from src.prompts.summary_prompts import (
    search_summary_system_prompt_zh_v1,
    summary_system_prompt_zh,
    search_summary_user_prompt_zh,
    summary_user_prompt_zh,
)
from src.prompts.pre_intent_prompts import (
    pre_intent_system_zh,
    pre_intent_user_zh
)
from src.utils.actions.other_actions.topk_base import TopkBase
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.memory.memory_base import MemoryBase
from src.utils.utils import (
    stage0_parse_intent,
    stage1_format2query_with_link,
    stage3_format_web_fetch,
    stage3_format_rest_query_with_link,
)
from src.utils.web_content_scrape import WebScrape, WebScrapePy


@dataclass
class WebChatQueryRequest:
    query: str
    message_id: str
    user_portrait: Optional[str] = None
    position: Optional[str] = "北京"
    request_id: Optional[str] = None
    model_name: Optional[str] = None
    resource: str = "default"


class WebSearchAgent:
    def __init__(
            self,
            intent_llm: OpenAI_LLM,
            summary_llm: OpenAI_LLM,
            topk_method: Union[TopkBase, None],
            action_executor: ActionExecutor,
            src_source: str,
            public_memory: MemoryBase,
            crawler: Union[WebScrape, WebScrapePy],
            dojo: None = None,
            detect: bool = False,
            sensitive_config: Optional[Dict[str, str]] = None,
            search_nums: int = 10,
            rest_k: int = 5,
            using_cached_link_first: bool = False,
            reranker_using_title: bool = False,
            truncate_length: int = 1500,
            router_system_prompt: str = router_system_zh,
            router_user_prompt: str = router_user_zh,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.summary_llm = summary_llm
        self.pre_intent_llm = summary_llm
        self.topk_method = topk_method
        self.crawler = crawler
        self.public_memory = public_memory
        self.detect = detect
        self.dojo = dojo
        self.src_source = src_source
        if sensitive_config is None:
            self.sensitive_config = {}
        else:
            self.sensitive_config = sensitive_config
        self.rest_k = rest_k
        self.search_nums = search_nums
        self.using_cached_link_first = using_cached_link_first
        self.reranker_using_title = reranker_using_title
        self.truncate_length = truncate_length
        self.router_system_prompt = router_system_prompt
        self.router_user_prompt = router_user_prompt

    async def stage0_intent_rewrite(self, query, position="北京", user_portrait=None):
        input_query = self.router_user_prompt.format( user_input=query, user_portrait=user_portrait)

        router_system_prompt = self.router_system_prompt.format(
            position=position, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        # logger.error(self.public_memory)
        messages = (
                [{"role": "system", "content": router_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": input_query}]
        )
        logger.info(f"intent messages: {messages}")
        intent_json_output = None
        error = None
        try:
            intent_return,chat_error = await self.intent_llm.chat(
                messages,
                stream=False,
            )
            error = chat_error
            intent_json_output = stage0_parse_intent(query, intent_return)
        except APIError as e:
            error = f"Fail to call rewrite model, e: {str(e)}"
        return intent_json_output,error

    async def stage0_pre_intent(self, query):
        pre_intent_user_prompt = pre_intent_user_zh.format(user_input=query)

        pre_intent_system_prompt = pre_intent_system_zh

        for msg_dict in self.public_memory.get_past_messages():
            if msg_dict["role"] == "assistant":
                msg_dict["content"] = re.sub(r'(\[ref_\d+\]|!\[.*?\]\(.*?\))', '', msg_dict["content"])

        messages = (
                [{"role": "system", "content": pre_intent_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": pre_intent_user_prompt}]
        )

        logger.info(f"stage0_pre_intent: {messages}")

        error = None
        res = None
        try:
            res,chat_error = await self.pre_intent_llm.chat(
                messages,
                stream=False,
            )
            error = chat_error
        except APIError as e:
            error = f"Fail to finish pre intent, e: {str(e)}"
            # logger.info(f"error: {error}")
        return res,error

    async def stage0_knowledge(self, query):
        pass

    async def stage0_guide(self, query):
        guide_user_prompt = guide_user_prompt_zh.format(user_input=query)

        guide_system_prompt = guide_system_prompt_zh.format(
            formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        messages = (
                [{"role": "system", "content": guide_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": guide_user_prompt}]
        )
        logger.info(f"stage0_guide messages:   {messages}")

        res = await self.intent_llm.chat(
            messages,
            stream=False,
        )

        return res

    async def stage1_web_search(self, search_queries):
        """
        Perform asynchronous web searches for a list of queries, USING MULTI search engine if theres more than one.

        Args:
        - search_queries: A list of query strings. Each string represents a search query to be executed.

        e.g.
        search_queries: [商汤 股票 价格，商汤 股票 2024]

        Returns:
        - A dictionary where the keys are the original search queries and the values are the corresponding search results.

        self.action_executor("web_search", single_query) searchs each query using multiple search engine (if), see .
        """

        results = await asyncio.gather(
            *(
                self.action_executor("web_search", query, self.search_nums)
                for query in search_queries
            )
        )
        error_list = []
        error_msg = None
        # search_engines = self.action_executor.actions_classes[0].search_engines[0].engine_name

        for result in results:
            src = next(iter(result.values()))
            if "Error" in src:
                error_list.append(src)
        if 0 < len(results) == len(error_list):
            error_msg = error_list[0]

        temp_results = {query: result for query, result in zip(search_queries, results)}
        """
        temp_results:
        {
            "商汤 股票 价格": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                }
            },
            "商汤 股票 2024": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    ### empty if engine 2 has an error
                }
            }
        }
        """
        # pprint(temp_results)
        query_with_link, fields_data, query_snippets, query_links = (
            stage1_format2query_with_link(
                temp_results,
                cached_link_first=self.using_cached_link_first,
                include_title=self.reranker_using_title,
            )
        )
        # logger.error(query_with_link)
        return query_with_link, fields_data, query_snippets, query_links, error_msg

    async def stage2_3(
            self,
            ori_query: str,
            query_with_link: dict,
            query_snippets: dict,
            query_links: dict,
    ):

        logger.info("fetch start")
        fetch_start_time = time.perf_counter()
        query_scrape_res_task = asyncio.create_task(
            self.crawler.scrape(ori_query, query_links)
        )

        topk_query_with_link, res_query_with_link,error_list = await run_in_threadpool(
            self.topk_method.ranking, query_with_link, query_snippets
        )

        logger.info(f"fetch rerank end-----cost time:{round(time.perf_counter() - fetch_start_time,4)}----- link:{topk_query_with_link}")
        query_scrape_res, error_scrape_rate = await query_scrape_res_task
        logger.info(f"fetch crawler end-----cost time:{round(time.perf_counter() - fetch_start_time,4)}")
        llm_main_ref, llm_rest_ref = stage3_format_web_fetch(
            query_scrape_res,
            topk_query_with_link,
            res_query_with_link,
            truncate_length=self.truncate_length,
            rest_k=self.rest_k,
        )
        return llm_main_ref, llm_rest_ref,error_list

    async def stage4_llm_summary(
            self,
            query,
            reference_content_,
            rest_ref,
            request_id,
            position="北京",
            user_portrait=None,
            search=True,
    ):
        # if pre_text == "" or pre_text is None:

        if search:
            # reference_content_ += rest_ref
            reference_content_ = reference_content_.replace("疫情", "")
            summary_user_prompt = search_summary_user_prompt_zh.format(
                user_portrait=user_portrait,
                web_content=reference_content_,
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            logger.info(f"position = {position}")
            summary_system_prompt = search_summary_system_prompt_zh_v1.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                position=position
            )
        else:
            summary_user_prompt = summary_user_prompt_zh.format(
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            summary_system_prompt = summary_system_prompt_zh.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        messages = (
                [{"role": "system", "content": summary_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": summary_user_prompt}]
        )
        # logger.debug(f"stage4_llm_summary messages:   {messages}")
        logger.info(f"stage4_llm_summary messages:   {messages}")
        logger.info(f"stage4_llm_summary web content:   {reference_content_}")
        full_response = ""
        is_first_token = True
        async for chunk in self.summary_llm.yield_chat_from_db(
                messages,
                stream=True,
        ):
            full_response += chunk["content"]
            # send notification of when first token from llm appears
            if is_first_token:
                is_first_token = False
                mft = {
                    "data": "",
                    "type": "t6-mft",
                }
                yield mft


            yield chunk


    async def stage5_follow_up_question(self, user_query, llm_response):
        error = None
        state5_result = None
        if not self.public_memory.get_past_messages():
            self.public_memory.add_message(user_query, "user")
            self.public_memory.add_message(llm_response, "assistant")
        memory = self.public_memory.get_past_messages()
        query = memory[-2]["content"] + "\n" + memory[-1]["content"] + "\n"
        follow_up_prompt = follow_up_prompts.format(chat_his=query)
        # 没有system prompt

        messages = [{"role": "user", "content": follow_up_prompt}]
        logger.info(f"stage5_follow_up_question messages:   {messages}")
        try:
            state5_result,chat_error = await self.intent_llm.chat(
                messages,
                stream=True,
            )
        except APIError as e:
            error = f"Chat follow up http error, e: {str(e)}"
        logger.info(f"stage5_follow_up_question state5: {state5_result}")
        return state5_result,error

    async def stage5_follow_up_question_http(self):
        # memory = self.summary_llm.memory.get_all_messages()
        query = (
                self.public_memory.get_past_messages()[-2]["content"]
                + "\n"
                + self.public_memory.get_past_messages()[-1]["content"]
                + "\n"
        )
        query = follow_up_prompts.format(chat_his=query)
        full_response = ""
        async for chunk in self.intent_llm.yield_chat(query):
            full_response += chunk["content"]
            yield chunk["content"]

    def convert_query_with_link_to_frontend_ref(self, data):
        results = []
        i = 1
        for key, articles in data.items():
            for _, article in articles.items():
                result = {
                    "index": i,
                    "title": article["title"],
                    "url": article["link"],
                    "url_source": "",
                    "icon": "",
                }
                if "wiki" in result["title"] or result["title"] == "":
                    continue
                results.append(result)
                i += 1
        return results

    @staticmethod
    async def pure_web_search(query: str, tools: List[WebSearch], search_nums=10):
        action_executor = ActionExecutor(tools)

        tasks = []
        tasks.append(action_executor("web_search", query, search_nums))
        results = await asyncio.gather(*tasks)

        temp_results = {}
        temp_results[query] = results[0]
        query_with_link, fields_data, query_snippets, query_links = (
            stage1_format2query_with_link(
                temp_results, cached_link_first=False, include_title=False
            )
        )
        return query_with_link

    async def chat(self, request: WebChatQueryRequest):
        try:
            query = request.query
            user_portrait = request.user_portrait
            position = request.position
            message_id = request.message_id
            request_id = request.request_id
            model_name = request.model_name
            resource = request.resource

            ####################################### 输入接入nova检测敏感词#######################################
            # session_id = str(uuid.uuid4())
            # user_id = "user_online_search"  # 在线搜索接口使用
            # ext_info = {
            #     "model": model_name,
            #     "resource": resource,
            #     "user_id": user_id,
            # }

            # if self.detect:
            #     request_data = TextModerationRequest(
            #         self.sensitive_config["ak"],
            #         self.sensitive_config["sk"],
            #         self.sensitive_config["app_id"],
            #         user_id,
            #         query,
            #         "LLMPrompt",
            #         session_id,
            #         ext_info=ext_info,
            #     )
            #     result = await moderate_text_generic(
            #         str(request_id), request_data, "输入"
            #     )
            #     if result:
            #         response = {
            #             "data": " 。(尊敬的用户您好，目前暂时无法回答这个话题哦～)",
            #             "type": "message",
            #             "messageId": message_id,
            #         }
            #         yield response
            #         return
            # TODO: -------------------> 下述moderation请求，在stage4作校验？
            ####################################### 输出接入nova检测敏感词，入参定义#######################################
            # text_id = str(uuid.uuid4())
            # request_data = TextModerationRequest(
            #     self.sensitive_config["ak"],
            #     self.sensitive_config["sk"],
            #     self.sensitive_config["app_id"],
            #     user_id,
            #     "",
            #     "LLMStreamResponse",
            #     session_id,
            #     text_id=text_id,
            #     finish_detect=False,
            #     ext_info=ext_info,
            # )
            ####################################### 输出接入nova检测敏感词，入参定义#######################################

            llm_responds = ""
            query_with_link = None

            stage0_pre_intent_task = asyncio.create_task(self.stage0_pre_intent(query))

            pre_start_time = time.perf_counter()
            logger.info(f"pre intent start")
            pre_intent_res,error = await stage0_pre_intent_task
            if error:
                if error == "sensitive":
                    error_code = news_error_code.error_code_pre_intent_result_sensitive
                elif error == "empty content":
                    error_code = news_error_code.error_code_pre_intent_result_format_error
                else:
                    error_code = news_error_code.error_code_pre_intent_http_error
                err_code = {
                    "data": error,
                    "code": error_code,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                return
            if pre_intent_res not in ["其他新闻","不相关问题","国际/军事新闻"]:
                err_code = {
                    "data": "pre intent format error",
                    "code": news_error_code.error_code_pre_intent_result_format_error,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code

            logger.info(
                f"pre intent end-----cost time: {round(time.perf_counter() - pre_start_time, 4)}s-----result: {pre_intent_res}")
            pre_result = {
                "data": "",
                "type": "t3-pre_intent",
                "messageId": message_id,
            }
            ## disable keywords yield for TTFT testing
            yield pre_result

            if pre_intent_res == "不相关问题":
                err_code = {
                    "data": "Not news relative conversation, should deny.",
                    "code": news_error_code.error_code_irrelative_intent,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code

                response_data_list = ['这个', '问题', '有点', '超出', '我', '的', '知识', '范畴', '啦', '']
                for data in response_data_list:
                    # 模拟异步延迟
                    await asyncio.sleep(0.1)
                    yield {"data": data, "type": "message", "messageId": str(message_id)}
                end_result = {"type": "messageEnd", "messageId": message_id, "code": 0}
                yield end_result
                return

            # loop = asyncio.get_running_loop()
            # with ThreadPoolExecutor() as executor:
            #     guiding_task = loop.run_in_executor(
            #         executor, self.stage0_guide, query
            #     )
            #     guiding_res = await guiding_task
            # guiding_res = await self.stage0_guide(query)
            # logger.info(f"stage0_guide result: {guiding_res}")

            if (pre_intent_res == "其他新闻" and self.src_source == "hybrid") or self.src_source == "byte":
                BEARER_TOKEN = config["byte_dance"]["api_key"]
                bot_id = config["byte_dance"]["bot_id"]
                url = "https://open.feedcoopapi.com/agent_api/agent/chat/completion"

                headers = {
                    "Authorization": f"Bearer {BEARER_TOKEN}",
                    "Content-Type": "application/json"
                }

                messages = (
                        [{"role": "system",
                          "content": "你是一个新闻整合助手，用中文进行回复。必要时请结合用户画像和当前位置进行新闻信息的筛选与整合。用Markdown格式回复，同时先带上加粗体格式标题，标题为结合用户问题与你后续输出内容的总结。输出整合的新闻内容5条左右即可，最多8条。"
                                     "\n注意：\n1.如果用户的问题中包含了当前位置需求，比如\"我这里最近的新闻\"、\"附近有什么新闻\"，\"我这边有啥新闻\"，\"当地新闻\"，表明用户询问当前位置的新闻，你需要筛选当前位置的信息进行总结分析"
                                     "\n2.当用户输入中没有明显要求某一领域的时候（如：最近有什么新闻），你需要结合用户画像进行筛选（如：用户喜欢金融、数码类新闻，你需要筛选这两类的新闻进行整合），如果用户有明确指向（如：最近有什么体育新闻），那就已用户的要求为准。"}]
                        + self.public_memory.get_past_messages()
                        + [{"role": "user",
                            "content": f"用户画像：{user_portrait}；当前位置：{position}；当前输入：{query}"}]
                )
                payload = {
                    "bot_id": str(bot_id),
                    "stream": True,
                    "messages": messages,
                    "user_id": "hq53qh6683",

                }
                is_first_token = True

                async with aiohttp.ClientSession() as session:
                    async with session.post(url=url, headers=headers, json=payload) as response:
                        if not response.ok:
                            result = {
                                "data": f"status: {response.status} message: {response.reason}",
                                "code": news_error_code.error_code_byte_api_http_error,
                                "type": "error",
                                "messageId": message_id,
                            }
                            yield result
                            return
                        try:
                            async for line in response.content:
                                line_str = line.decode('utf-8').strip()

                                if line_str and line_str.startswith("data:"):
                                    if is_first_token:
                                        is_first_token = False
                                        mft = {
                                            "data": "",
                                            "type": "t6-mft",
                                        }
                                        yield mft

                                    if line_str == "data:[DONE]": continue
                                    event = json.loads(line_str[5:])
                                    if "error" in event:
                                        result = {
                                            "data": f'business error:{event["error"]["message"]}',
                                            "code": news_error_code.error_code_byte_api_return_error,
                                            "type": "error",
                                            "messageId": message_id,
                                        }
                                        yield result
                                    elif "ResponseMetadata" in event:
                                        result = {
                                            "data": f'gateway error:{event["ResponseMetadata"]["Error"]["Message"]}',
                                            "code": news_error_code.error_code_byte_api_return_error,
                                            "type": "error",
                                            "messageId": message_id,
                                        }
                                        yield result
                                    elif "references" in event and event["references"]:
                                        ref = event["references"]
                                        ref_list = []
                                        if ref:
                                            logger.info(f"references:{ref}")
                                            if isinstance(ref, list):
                                                for ref_item in ref:
                                                    var = {
                                                        'title': ref_item["title"],
                                                        'url': ref_item["url"],
                                                        'site_name': ref_item["site_name"],
                                                        'icon': ref_item["cover_image"]["url"] if ref_item["cover_image"] else ""
                                                    }
                                                    ref_list.append(var)

                                            result = {
                                                "data": ref_list,
                                                "type": "t4-src",
                                                "messageId": message_id,
                                            }
                                            yield result
                                    elif "follow_ups" in event and event["follow_ups"]:
                                        follow_ups = event["follow_ups"]
                                        follow_up_list = []
                                        logger.info(f"follow_ups: {follow_ups}")

                                        if "usage" in event:
                                            usage_result = {
                                                "data": "",
                                                "type": "message",
                                                "usage": event["usage"],
                                                "messageId": message_id,
                                            }
                                            yield usage_result

                                        if follow_ups:
                                            if isinstance(follow_ups, list):
                                                for item in follow_ups:
                                                    follow_up_list.append(item["item"])
                                                question_dict = {"follow_ups": follow_up_list}
                                                follow_result = {
                                                    "data": question_dict,
                                                    "type": "follow_ups",
                                                    "messageId": message_id,
                                                }
                                                yield follow_result



                                    else:
                                        llm_responds += event["choices"][0]["delta"]["content"]
                                        result = {
                                            "data": event["choices"][0]["delta"]["content"],
                                            "type": "message",
                                            "messageId": message_id,
                                        }
                                        yield result


                        except Exception as e:
                            logger.info(f"exception: {e}")
                            error_result = {
                                "data": str(e),
                                "code": news_error_code.error_code_byte_api_http_error,
                                "type": "error",
                                "messageId": message_id,
                            }
                            yield error_result

                yield {"type": "messageEnd", "messageId": message_id, "code": 0}
                return

            start_time = time.perf_counter()
            logger.info(f"intent start")
            stage0_intent_rewrite_task = asyncio.create_task(
                self.stage0_intent_rewrite(query, position, user_portrait))
            intent_json_output,error = await stage0_intent_rewrite_task
            if error:
                if error == "empty content":
                    error_code = news_error_code.error_code_rewrite_search_list_empty
                else:
                    error_code = news_error_code.error_code_rewrite_http_error
                err_code = {
                    "data": error,
                    "code": error_code,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                return
            logger.info(
                f"intent end-----cost time: {round(time.perf_counter() - start_time, 4)}s-----result: {intent_json_output}")
            logger.info(f"intent_json_output: {intent_json_output}, took {round(time.perf_counter() - start_time, 4)}s")

            logger.info(
                "stage0_intent_rewrite_result FINISH",
            )
            if len(intent_json_output[query]) > 3:
                intent_json_output[query] = intent_json_output[query][:2]  # TODO: -------------------> 只取前两个关键字？

            # intent_json_output = await self.stage0_intent_rewrite(query)
            # logger.info(f"stage0_intent_rewrite result: {intent_json_output}")
            llm_summary_fist_word_time = 0
            llm_summary_fist_word_time_detect = 0
            llm_summary_start = 0
            code = 0
            search_list = intent_json_output.get(query, [])

            if not search_list:
                err_code = {
                    "data": "search_list(keywords) is null or empty",
                    "code": news_error_code.error_code_rewrite_search_list_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                return

            keywords = {
                "data": search_list,
                "type": "t3-intent",
                "messageId": message_id,
            }
            ## disable keywords yield for TTFT testing
            yield keywords

            logger.info("src start")
            src_start_time = time.perf_counter()
            stage1_task = asyncio.create_task(self.stage1_web_search(search_list))
            (
                query_with_link,
                fields_data,
                query_snippets,
                query_links,
                error
            ) = await stage1_task
            if error:
                err_code = {
                    "data": error,
                    "code": news_error_code.error_code_src_search_http_error,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
            logger.info(f"src end-----cost time: {round(time.perf_counter() - src_start_time,4)}s-----link: {query_with_link}")
            # search_engine_error, query_with_link = await self.stage1_web_search(search_list)
            logger.info("stage1_web_search FINISH")

            if all(not value for value in query_with_link.values()):
                logger.warning("所有搜索引擎都没搜到")
                err_code = {
                    "data": "There is nothing from web search.",
                    "code": news_error_code.error_code_src_search_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code

                return
            from src.utils.llm_content_helper import ContentHelper
            content_helper = ContentHelper()
            if self.topk_method is None:  # mode 为 light
                guiding_res = ""  # await stage0_guide_task
                logger.info(f"stage0_guide result: {guiding_res}")
                frontend_ref = self.convert_query_with_link_to_frontend_ref(
                    query_with_link
                )
                logger.info(f"frontend_ref: {frontend_ref}")
                sources = {
                    "data": frontend_ref,
                    "type": "t4-src",
                    "messageId": message_id,
                }
                yield sources
                llm_whole_ref = stage3_format_rest_query_with_link(
                    query_with_link, 0
                )
                async for chunk in self.stage4_llm_summary(
                        query,
                        llm_whole_ref,
                        "None",
                        request_id,
                        guiding_res,
                        search=False,
                ):
                    if "content" not in chunk:
                        chunk['messageId'] = message_id
                        yield chunk
                        continue
                    llm_responds += chunk["content"]
                    result = {
                        "data": chunk["content"],
                        "type": "message",
                        "messageId": message_id,
                    }
                    if "usage" in chunk:
                        result["usage"] = chunk["usage"]
                    yield result
                logger.info(f"stage4 result: {llm_responds}")
                # return llm_responds, query_with_link, intent_json_output
            else:
                frontend_ref = self.convert_query_with_link_to_frontend_ref(
                    query_with_link
                )
                logger.info(f"frontend_ref: {frontend_ref}")
                sources = {
                    "data": frontend_ref,
                    "type": "t4-src",
                    "messageId": message_id,
                }
                yield sources
                llm_main_ref, llm_rest_ref,error_list = await self.stage2_3(
                    query, query_with_link, query_snippets, query_links
                )
                if error_list:
                    for error in error_list:
                        err_code = {
                            "data": error["msg"],
                            "code": error["code"],
                            "type": "error",
                            "messageId": message_id
                        }
                        yield err_code
                llm_whole_ref = llm_main_ref + llm_rest_ref
                fetch = {
                    "data": llm_whole_ref,
                    "type": "t5-fetch",
                    "messageId": message_id,
                }
                yield fetch
                # logger.debug(
                #     f"stage2_3 result: \n\n {llm_whole_ref} \n\n {rest_ref}")
                logger.info("llm_summary start")

                llm_summary_start = time.perf_counter()
                summary_error = None
                # 此处非light模式不用guide
                try:
                    async for chunk in self.stage4_llm_summary(
                            query,
                            llm_whole_ref,
                            llm_rest_ref,
                            request_id,
                            position=position,
                            user_portrait=user_portrait,
                            search=True,
                    ):
                        if "type" in chunk:
                            if "t6-mft" == chunk["type"]:
                                llm_summary_fist_word_time = round(time.perf_counter() - llm_summary_start, 4)
                            elif "t7-sft" == chunk["type"]:
                                llm_summary_fist_word_time_detect = round(time.perf_counter() - llm_summary_start, 4)
                        if "content" not in chunk:
                            chunk['messageId'] = message_id
                            yield chunk
                            continue
                        llm_responds += chunk["content"]
                        content_helper.append_content(chunk["content"])
                        result = {
                            "data": chunk["content"],
                            "type": "message",
                            "messageId": message_id,
                        }
                        if chunk.get("code", 0) == 18:
                            result["code"] = news_error_code.error_code_chat_summary_llm_error
                            result["type"] = "error"
                            result["data"] = f"llm summary sensitive error"
                            code = chunk["code"]
                            yield result
                            return
                        else:
                            if content_helper.tts_start:
                                yield result
                        if "usage" in chunk:
                            result["usage"] = chunk["usage"]

                except APIError as e:
                    summary_error = e.message

                if summary_error:
                    err_code = {
                        "data": summary_error,
                        "code": news_error_code.error_code_chat_summary_http_error,
                        "type": "error",
                        "messageId": message_id,
                    }
                    yield err_code
                    return

            follow_up_start_time = time.perf_counter()
            logger.info("follow up start")
            summary_title = content_helper.title
            logger.info(f"summary_title: {summary_title}")
            conclusion_data = {"summary_title": summary_title,"follow_ups":[]}
            question,error = await self.stage5_follow_up_question(query, llm_responds)
            if error:
                err_code = {
                    "data": error,
                    "code": news_error_code.error_code_relative_http_error,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
            logger.info(
                f"follow up end-----cost time: {round(time.perf_counter() - follow_up_start_time, 4)}s-----result: {question}")
            if question:
                try:
                    conclusion_data["follow_ups"] = ast.literal_eval(question)
                except BaseException as e:
                    err_code = {
                        "data": f"Chat conclusion title format error, e: {str(e)}",
                        "code": news_error_code.error_code_relative_format_error,
                        "type": "error",
                        "messageId": message_id,
                    }
                    yield err_code

                result = {
                    "data": conclusion_data,
                    "type": "follow_ups",
                    "messageId": message_id,
                }
                yield result

            result = {"type": "messageEnd", "messageId": message_id, "code": code}

            logger.info(
                    f"llm_summary end-----detect:{self.detect}-----first token time: {llm_summary_fist_word_time if not self.detect else llm_summary_fist_word_time_detect}-----total time: {round(time.perf_counter() - llm_summary_start, 4)}")

            yield result
        except openai.BadRequestError as e:
            err_code = e.code
            message = e.response.json().get('error', {'message': ""}).get('message')
            result = {"type": "messageEnd", "messageId": message_id, "data": message, "code": err_code}
            yield result

        except Exception as e:
            logger.info(e)
            import traceback

            traceback.print_exc()

# if __name__ == "__main__":
#     from src.utils.llms.openai_SDK import OpenAI_LLM
#     from src.utils.actions.tool_actions.serper import Serper
#     from src.utils.actions.tool_actions.web_search import WebSearch
#     from src.utils.memory.memory_base import (
#         AllMessageMemory,
#         ZeroMessageMemory,
#         MemoryBase,
#     )
#     from src.utils.actions.tool_actions.action_excutor import ActionExecutor
#     from src.utils.actions.other_actions.rerank_topk import Rerank
#
#     global_config = configuration.config
#     selected_llm = global_config["llm_server_set"][global_config["selected_llm_server"]]
#     print("selected_llm", selected_llm)
#     print("rewrite", global_config["rewrite"])
#
#     model_info = {
#         "rewriter": {
#             # "api_key": global_config["rewrite"]["api_key"],
#             # "base_url": global_config["rewrite"]["base_url"],
#             "temperature": 0,
#             "top_p": 0.5,
#             "max_tokens": 4096,
#         },
#         "summary": {
#             "api_key": selected_llm["api_key"],
#             "base_url": selected_llm["base_url"],
#             "temperature": 0.1,
#             "top_p": 0.5,
#             "max_tokens": 4096,
#         },
#     }
#
#     summary_mem = ZeroMessageMemory()
#     # model_name = "senseauto-chat-v0.2.0"
#
#     k = 10
#     rest_k = 5
#     sougou_se = SouGou(
#         sougou_config=global_config["search_engine"]["sougou"],
#         sougou_full_config=global_config["search_engine"]["sougou_full"],
#         engine_name="sougou",
#     )
#     bing_se = Bing(
#         bing_config=global_config["search_engine"]["bing"], engine_name="bing"
#     )
#     serper_se = Serper(global_config["search_engine"]["serper"], "google")
#     tools = [WebSearch(search_engines=[bing_se])]
#     limit_scraping_time = "1200ms"
#
#     crawler = WebScrape(
#         global_config["scrape"], limit_scraping_time=limit_scraping_time
#     )
#     tool_executor = ActionExecutor(tools)
#     public_memory = AllMessageMemory()
#
#     rerank_mudole = Rerank(k, global_config["rerank"]["base_url"])
#
#     intent_llm = OpenAI_LLM(model_info["rewriter"])
#     sumamry_llm = OpenAI_LLM(model_info["summary"])
#     web = WebSearchAgent(
#         intent_llm,
#         sumamry_llm,
#         rerank_mudole,
#         tool_executor,
#         public_memory,
#         crawler,
#         None,
#         sensitive_config=global_config["sensitive"],
#     )
#
#
#     async def main():
#         while True:
#             query = input("Enter query (or type -q to quit): ")
#             if query == "-q":
#                 break
#             else:
#                 web_chat_query_request = WebChatQueryRequest(
#                     query=query, message_id=secrets.token_hex(7)
#                 )
#
#                 message = ""
#                 async for response in web.chat(web_chat_query_request):
#                     # print(response)
#                     if "llm_responds" in response:
#                         print(f"Final response: {response['llm_responds']}")
#                         continue
#
#                     if "type" in response:
#                         response_type = response["type"]
#                         if response_type == "message" and "data" in response:
#                             print(response["data"], end="")
#                             message += response["data"]
#
#                 # 打印累积的完整消息
#                 print(f"\nAccumulated message: {message}")
#
#
#     asyncio.run(main())
